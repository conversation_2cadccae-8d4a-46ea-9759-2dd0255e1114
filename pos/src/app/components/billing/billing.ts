import { CommonModule } from '@angular/common';
import { Component, Input, Output, EventEmitter, OnChanges, SimpleChanges, ChangeDetectorRef } from '@angular/core';
import { FormsModule } from '@angular/forms';
import { TableModule } from 'primeng/table';
import { InputNumberModule } from 'primeng/inputnumber';
import { DividerModule } from 'primeng/divider';
import { ButtonModule } from 'primeng/button';
import { CommonService } from '../../services/common.service';
import { ProgressSpinnerModule } from 'primeng/progressspinner';
import { NoDataComponent } from "../no-data/no-data";
import { StorageService } from '../../services/storage.service';
import { OrderService } from '../../services/order.service';
import { PrintService } from '../../services/print.service';
import { FreebieService } from '../../services/freebie.service';
import { CartItem, OrderItem } from '../../models';
import { TableComponent } from "../table/table";
import { PaymentModalComponent } from '../payment-modal/payment-modal';
import { DialogModule } from 'primeng/dialog';
import { MessageService } from 'primeng/api';

@Component({
  selector: 'app-billing',
  standalone: true,
  templateUrl: './billing.html',
  imports: [
    CommonModule,
    FormsModule,
    TableModule,
    InputNumberModule,
    DividerModule,
    ButtonModule,
    ProgressSpinnerModule,
    NoDataComponent,
    TableComponent,
    PaymentModalComponent,
    DialogModule
],
  providers: [MessageService]
})
export class BillingComponent implements OnChanges {
  showConfirmDialog = false;
  @Input() showTable = false;
  @Input() cartItems: CartItem[] = [];
  @Input() quantityEdit = false;
  @Input() showActions = false;
  @Output() cartChange = new EventEmitter<CartItem[]>();
  @Output() cartCleared = new EventEmitter<void>();
  @Input() cartColumns = [
    { field: 'thumbnail_image', header: 'Image', type: 'image' },
    { field: 'name', header: 'SKU' },
    { field: 'selling_price', header: 'Price' },
    { field: 'quantity', header: 'Quantity'}
  ]
  @Input() noDataTitle = 'Add items to cart';
  @Input() noDataMessage = '';
  isProcessingCheckout = false;
  showPaymentModal = false;

  constructor(
    private commonService: CommonService,
    private cdr: ChangeDetectorRef,
    private storageService: StorageService,
    private orderService: OrderService,
    private printService: PrintService,
    private messageService: MessageService,
    private freebieService: FreebieService
  ) { }

  ngOnChanges(_changes: SimpleChanges): void {
    console.log(this.cartItems);
  }

  removeFromCart(product: CartItem) {
    const index = this.cartItems.findIndex((item) => item.id === product.id);
    if (index > -1) {
      this.cartItems.splice(index, 1);
      this.cartChange.emit([...this.cartItems]);
    }
  }

  updateQuantity(event: { value: number }, product: CartItem) {
    const newQuantity = event.value;
    if (newQuantity < 1) {
      this.removeFromCart(product);
    } else {
      product.quantity = newQuantity;
      this.cartChange.emit([...this.cartItems]);
    }
  }

  getSubTotal() {
    return this.cartItems.reduce((total, item) => total + (item.selling_price || 0) * item.quantity, 0);
  }

  getTaxes() {
    return this.cartItems.reduce((total, item) => {
      if (item.taxable) {
        return total + (item.tax / 100) * (item.selling_price || 0) * item.quantity;
      }
      return total;
    }, 0);
  }

  getGrandTotal() {
    return this.getSubTotal() + this.getTaxes();
  }

  clearCart() {
    this.cartItems.length = 0;
    this.cartChange.emit([...this.cartItems]);
    this.cartCleared.emit(); // Emit event to clear freebies
    this.cdr.detectChanges();
  }

  confirmCheckout() {
    this.showConfirmDialog = true;
  }

  onPaymentCancel() {
    this.showConfirmDialog = false;
  }

  createOrder(event: any) {
    try {
      const { orderItems, orderTotal, freebieCount } = this.prepareOrderData();

      if (!this.validateOrderItems(orderItems)) return;

      const orderData = {
        customerName: event.customerName,
        customerPhone: event.customerPhone,
        total: orderTotal,
        items: orderItems,
      };

      this.isProcessingCheckout = true;
      this.orderService.createOrder(orderData).then((order: any) => {
        const successMessage = this.buildOrderSuccessMessage(orderTotal, freebieCount);
        this.commonService.toast({ severity: 'success', summary: 'Success', detail: successMessage });
        if (order?.data?.order_id) {
          this.printReceipt(order.data.order_id);
        }
        this.clearCart();
        this.showConfirmDialog = false;
        this.isProcessingCheckout = false;
      }).catch((error: any) => {
        let errorMessage = 'Order creation failed. Please try again.';
        if (error.status === 401) {
          errorMessage = 'Authentication failed. Please check your token.';
        } else if (error.status === 400) {
          errorMessage = 'Invalid order data. Please check the items.';
        }
        this.commonService.toast({ severity: 'error', summary: 'Error', detail: errorMessage });
        this.isProcessingCheckout = false;
      })
    } catch (error) {
      this.commonService.toast({ severity: 'error', summary: 'Error', detail: 'Failed to create order. Please try again.' });
      this.isProcessingCheckout = false;
    }
  }

  onConfirmCheckout(): void {
    if (this.cartItems.length === 0) {
      this.messageService.add({
        severity: 'warn',
        summary: 'Empty Cart',
        detail: 'Please add items to cart before checkout'
      });
      return;
    }
    this.showPaymentModal = true;
    console.log('Payment modal should be visible now'); // Debug log
  }

  onPaymentConfirm(ev: any) {
    this.createOrder(ev);
    this.showPaymentModal = false;
  }

  onCancelCheckout() {
    this.showPaymentModal = false;
  }

  printReceipt(orderId?: string): void {
    if (this.cartItems.length === 0) {
      this.commonService.toast({ severity: 'warn', summary: 'Warning', detail: 'No items in cart to print' });
      return;
    }
    const user: any = this.storageService.getItem('user');
    const customerName = user?.displayName || 'Walk-in Customer';
    const orderIdToPrint = orderId || `TEMP-${Date.now()}`;

    const printData = {
      cartItems: this.cartItems,
      orderId: orderIdToPrint,
      customerName,
      paymentMethod: 'Cash',
      copyOfInvoice: false
    }
    this.printService.printCart(printData);
  }

  onAddUser(): void {
    // Here you can implement the logic to add a new user
    this.messageService.add({
      severity: 'info',
      summary: 'Add User',
      detail: 'Opening user creation dialog...'
    });
  }

  // Helper methods for cleaner order creation
  private prepareOrderData() {
    const paidItems = this.cartItems.filter(item => !(item as any).is_freebie && item.selling_price > 0);
    const freebieCount = this.cartItems.filter(item => (item as any).is_freebie).length;

    const orderItems = paidItems.map((item): Omit<OrderItem, 'total_price'> => ({
      sku: item.child_sku,
      unit_price: item.selling_price,
      sale_price: item.selling_price,
      quantity: item.quantity,
      tax: item.tax,
    }));

    const orderTotal = orderItems.reduce((total, item) =>
      total + (item.sale_price * item.quantity), 0);

    return { orderItems, orderTotal, freebieCount };
  }

  private validateOrderItems(orderItems: any[]): boolean {
    if (orderItems.length === 0) {
      this.commonService.toast({
        severity: 'warn',
        summary: 'Warning',
        detail: 'Cannot create order with only freebie items. Please add at least one paid item.'
      });
      return false;
    }
    return true;
  }

  private buildOrderSuccessMessage(orderTotal: number, freebieCount: number): string {
    const baseMessage = `Order created successfully! Total: ₹${orderTotal.toFixed(2)}`;
    return freebieCount > 0
      ? `${baseMessage} (${freebieCount} freebie${freebieCount > 1 ? 's' : ''} included)`
      : baseMessage;
  }
}
