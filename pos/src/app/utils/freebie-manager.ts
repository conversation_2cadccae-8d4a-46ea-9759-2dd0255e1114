import { FreebieService } from '../services/freebie.service';
import { FreebieProduct } from '../models/freebie.model';
import { CartItem } from '../models/product.model';

export interface BillingTab {
  items: CartItem[];
  freebiesProducts: FreebieProduct[];
  showFreebies: boolean;
  currentCartAmount: number;
  addedFreebies: Set<string>;
}

export class FreebieManager {

  constructor(private freebieService: FreebieService) {}

  //Initialize freebies for a tab
  async initializeFreebies(tab: BillingTab, cartAmount: number): Promise<void> {
    tab.currentCartAmount = cartAmount;
    await this.updateFreebies(tab);
  }

  // Update freebies display for a tab
  async updateFreebies(tab: BillingTab): Promise<void> {
    const freebies = await this.freebieService.getAvailableFreebies(tab.currentCartAmount);
    tab.freebiesProducts = freebies;
    tab.showFreebies = freebies.length > 0;
  }

 // Add a freebie to the cart
  addFreebie(tab: BillingTab, freebie: FreebieProduct): void {
    const freebieItem = this.freebieService.createFreebieCartItem(freebie);
    tab.items.push(freebieItem);
    tab.addedFreebies.add(freebie.id);
  }

 // Remove all freebies from cart
  removeAllFreebies(tab: BillingTab): void {
    const initialLength = tab.items.length;
    tab.items = this.freebieService.removeAllFreebies(tab.items);
    if (tab.items.length < initialLength) {
      tab.addedFreebies.clear();
    }
  }

  //Add freebie manually (from freebies table)
  addFreebieManually(tab: BillingTab, freebie: FreebieProduct): boolean {
    console.log('FreebieManager.addFreebieManually - checking conditions:');
    console.log('Cart amount:', tab.currentCartAmount, 'Freebie amount:', freebie.amount);
    console.log('Can afford:', tab.currentCartAmount >= freebie.amount);
    console.log('Is already in cart:', this.freebieService.isFreebieInCart(tab.items, freebie.id));

    const canAdd = this.freebieService.canAddFreebie(tab.currentCartAmount, freebie, tab.items);
    console.log('canAddFreebie result:', canAdd);

    if (!canAdd) {
      return false;
    }
    this.addFreebie(tab, freebie);
    console.log('Freebie added successfully');
    return true;
  }

  // Handle item removal (check if it's a freebie)
  handleItemRemoval(tab: BillingTab, removedItem: any): void {
    if (this.isFreebieItem(removedItem)) {
      tab.addedFreebies.delete(removedItem.freebie_id);
    }
  }

 // Check and update freebies when cart amount changes
  async checkCartAmountAndUpdate(tab: BillingTab, newCartAmount: number): Promise<boolean> {
    if (newCartAmount === tab.currentCartAmount) {
      return false;
    }
    tab.currentCartAmount = newCartAmount;
    if (newCartAmount === 0) {
      this.clearFreebiesOnCartClear(tab);
    } else {
      await this.updateFreebies(tab);
    }
    return true;
  }
 // Clear all freebies when cart is cleared
  clearFreebiesOnCartClear(tab: BillingTab): void {
    this.removeAllFreebies(tab);
    this.resetFreebieDisplay(tab);
  }
 //Reset freebie display state
  private resetFreebieDisplay(tab: BillingTab): void {
    tab.freebiesProducts = [];
    tab.showFreebies = false;
    tab.currentCartAmount = 0;
  }

  // Check if an item is a freebie
  private isFreebieItem(item: any): boolean {
    return item?.is_freebie && item?.freebie_id;
  }

  // Get freebie button state for UI
  getFreebieButtonState(tab: BillingTab, freebie: FreebieProduct): { hidden: boolean; disabled: boolean } {
    const isInCart = this.freebieService.isFreebieInCart(tab.items, freebie.id);
    const canAfford = tab.currentCartAmount >= freebie.amount;
    return {
      hidden: isInCart,
      disabled: !canAfford || isInCart
    };
  }
}
